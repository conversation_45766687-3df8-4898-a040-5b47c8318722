{"name": "nutri-ai", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "go": "expo start --go", "tunnel": "expo start --tunnel --go", "android": "expo run:android", "ios": "expo run:ios", "ios:simulator": "expo run:ios --simulator", "ios:device": "expo run:ios --device", "web": "expo start --web", "build:ios": "eas build --platform ios", "build:ios:preview": "eas build --platform ios --profile preview", "build:android": "eas build --platform android", "build:all": "eas build --platform all", "rebuild:voice": "node scripts/rebuild-voice-support.js", "ios:check": "node scripts/ios-build-check.js", "prebuild:ios": "npm run ios:check", "prebuild:ios:preview": "npm run ios:check"}, "dependencies": {"@expo/config": "~8.1.1", "@expo/config-plugins": "~7.2.2", "@expo/prebuild-config": "~6.2.4", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/cli-server-api": "^18.0.0", "@react-native-voice/voice": "3.2.4", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~49.0.0", "expo-av": "~13.4.1", "expo-blur": "~12.4.1", "expo-build-properties": "^0.14.8", "expo-camera": "~13.4.4", "expo-clipboard": "~4.3.1", "expo-constants": "^17.1.7", "expo-dev-client": "~2.4.13", "expo-haptics": "~12.4.0", "expo-image-picker": "~14.3.2", "expo-linear-gradient": "~12.3.0", "expo-location": "~16.1.0", "expo-notifications": "^0.20.1", "expo-sensors": "~12.3.0", "expo-sharing": "~11.5.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-updates": "~0.18.19", "fbjs": "^3.0.5", "invariant": "^2.2.4", "lottie-react-native": "5.1.6", "metro-react-native-babel-transformer": "^0.77.0", "punycode": "^2.3.1", "react": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-haptic-feedback": "^2.3.3", "react-native-health": "^1.19.0", "react-native-linear-gradient": "^2.8.3", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-sensors": "^7.3.6", "react-native-svg": "13.9.0", "url": "^0.11.4", "util": "^0.12.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~18.2.14", "typescript": "~5.8.3"}, "private": true}